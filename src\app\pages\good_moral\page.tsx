"use client";

import { useState } from "react";
import <PERSON><PERSON><PERSON><PERSON> from "pizzip";
import Docxtemplater from "docxtemplater";
import ImageModule from "docxtemplater-image-module-free";
import { Button } from "@/components/ui/button";

export default function GoodMoralPage() {
  const [form, setForm] = useState({
    // Personal Information
    "last-name": "",
    "mid-name": "",
    "mid-initial": "",
    age: "",
    "pos-address": "",

    // Location Information
    prov: "",
    municipal: "",

    // Date Information
    day: "",
    month: "",
    year: "",

    // Official Information
    mayor: "",
    "ctc-no": "",
    "or-no": "",

    // Image
    image: "", // base64 image data
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
      const img = new Image();
      img.src = reader.result as string;

      img.onload = () => {
        const canvas = document.createElement("canvas");
        const size = Math.min(img.width, img.height);
        const sx = (img.width - size) / 2;
        const sy = (img.height - size) / 2;

        canvas.width = 192;
        canvas.height = 192;

        const ctx = canvas.getContext("2d");
        if (ctx) {
          ctx.drawImage(img, sx, sy, size, size, 0, 0, 192, 192);
          const base64 = canvas.toDataURL("image/jpeg").split(",")[1];
          setForm((prev) => ({ ...prev, image: base64 }));
        }
      };
    };
    reader.readAsDataURL(file);
  };

  const generateDocument = async () => {
    if (!form["last-name"] || !form.prov || !form.municipal) {
      alert("Please fill in all required fields");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/Good_Moral_Certificate.docx");
      if (!response.ok) throw new Error("Failed to load template");

      const content = await response.arrayBuffer();
      const zip = new PizZip(content);

      const modules = [];
      if (form.image) {
        const imageOpts = {
          centered: false,
          getImage: (tagValue: string) =>
            Uint8Array.from(atob(tagValue), (c) => c.charCodeAt(0)),
          getSize: (): [number, number] => [192, 192], // 2x2 inches at 72 DPI
        };
        modules.push(new ImageModule(imageOpts));
      }

      const doc = new Docxtemplater(zip, {
        modules,
        paragraphLoop: true,
        linebreaks: true,
      });

      const capitalizeWords = (text: string) => {
        return text
          .split(" ")
          .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join(" ");
      };

      const templateData: any = {
        prov: capitalizeWords(form.prov),
        municipal: capitalizeWords(form.municipal),
        "last-name": capitalizeWords(form["last-name"]),
        "mid-name": capitalizeWords(form["mid-name"]),
        "mid-initial": form["mid-initial"].toUpperCase(),
        age: form.age,
        "pos-address": capitalizeWords(form["pos-address"]),
        day: form.day,
        month: capitalizeWords(form.month),
        year: form.year,
        mayor: form.mayor.toUpperCase(),
        "ctc-no": form["ctc-no"],
        "or-no": form["or-no"],
      };

      if (form.image) templateData.image = form.image;

      doc.setData(templateData);
      doc.render();

      const blob = doc.getZip().generate({ type: "blob" });
      const blobWithType = new Blob([blob], {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      });

      const url = window.URL.createObjectURL(blobWithType);
      const a = document.createElement("a");
      a.href = url;
      a.download = "good-moral-certificate.docx";

      // Mobile workaround: open in new tab (iOS Safari prefers this sometimes)
      if (navigator.userAgent.match(/iPhone|iPad|Android/i)) {
        window.open(url, "_blank");
      } else {
        a.click();
      }
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error("Error generating document:", err);
      alert("Something went wrong. Try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6 text-center">
        Good Moral Certificate
      </h1>

      <div className="max-w-2xl mx-auto">
        <div className="space-y-6">
          <h2 className="text-lg font-semibold mb-4">Fill in the details</h2>

          <div className="flex justify-center mb-6">
            <div>
              <input
                id="image"
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
              <div
                onClick={() => document.getElementById("image")?.click()}
                className="w-32 h-32 border-2 border-dashed border-border rounded-lg flex items-center justify-center cursor-pointer hover:border-primary transition-colors bg-card"
              >
                {form.image ? (
                  <img
                    src={`data:image/jpeg;base64,${form.image}`}
                    alt="Preview"
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <div className="text-center text-muted-foreground">
                    <svg
                      className="w-8 h-8 mx-auto mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    <p className="text-sm">Click to upload photo</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="border border-border rounded-lg p-4 bg-card">
            <h3 className="text-md font-medium mb-3 text-card-foreground">
              Location Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="prov"
                  className="block text-sm font-medium mb-2"
                >
                  Province *
                </label>
                <input
                  id="prov"
                  type="text"
                  name="prov"
                  placeholder="Enter province"
                  value={form.prov}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="municipal"
                  className="block text-sm font-medium mb-2"
                >
                  Municipality *
                </label>
                <input
                  id="municipal"
                  type="text"
                  name="municipal"
                  placeholder="Enter municipality"
                  value={form.municipal}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                  required
                />
              </div>
            </div>
          </div>

          {/* Personal Information */}
          <div className="border border-border rounded-lg p-4 bg-card">
            <h3 className="text-md font-medium mb-3 text-card-foreground">
              Personal Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="last-name"
                  className="block text-sm font-medium mb-2"
                >
                  Last Name *
                </label>
                <input
                  id="last-name"
                  type="text"
                  name="last-name"
                  placeholder="Enter last name"
                  value={form["last-name"]}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="mid-name"
                  className="block text-sm font-medium mb-2"
                >
                  Middle Name
                </label>
                <input
                  id="mid-name"
                  type="text"
                  name="mid-name"
                  placeholder="Enter middle name"
                  value={form["mid-name"]}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
              <div>
                <label
                  htmlFor="mid-initial"
                  className="block text-sm font-medium mb-2"
                >
                  Middle Initial
                </label>
                <input
                  id="mid-initial"
                  type="text"
                  name="mid-initial"
                  placeholder="M.I."
                  value={form["mid-initial"]}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                  maxLength={2}
                />
              </div>
              <div>
                <label htmlFor="age" className="block text-sm font-medium mb-2">
                  Age
                </label>
                <input
                  id="age"
                  type="number"
                  name="age"
                  placeholder="Enter age"
                  value={form.age}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                  min="1"
                  max="120"
                />
              </div>
            </div>
            <div className="mt-4">
              <label
                htmlFor="pos-address"
                className="block text-sm font-medium mb-2"
              >
                Postal Address
              </label>
              <input
                id="pos-address"
                type="text"
                name="pos-address"
                placeholder="Enter postal address"
                value={form["pos-address"]}
                onChange={handleTextChange}
                className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
              />
            </div>
          </div>

          {/* Date Information */}
          <div className="border border-border rounded-lg p-4 bg-card">
            <h3 className="text-md font-medium mb-3 text-card-foreground">
              Date Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="day" className="block text-sm font-medium mb-2">
                  Day
                </label>
                <input
                  id="day"
                  type="number"
                  name="day"
                  placeholder="DD"
                  value={form.day}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                  min="1"
                  max="31"
                />
              </div>
              <div>
                <label
                  htmlFor="month"
                  className="block text-sm font-medium mb-2"
                >
                  Month
                </label>
                <input
                  id="month"
                  type="text"
                  name="month"
                  placeholder="Enter month"
                  value={form.month}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
              <div>
                <label
                  htmlFor="year"
                  className="block text-sm font-medium mb-2"
                >
                  Year
                </label>
                <input
                  id="year"
                  type="number"
                  name="year"
                  placeholder="YYYY"
                  value={form.year}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                  min="1900"
                  max="2100"
                />
              </div>
            </div>
          </div>

          {/* Official Information */}
          <div className="border border-border rounded-lg p-4 bg-card">
            <h3 className="text-md font-medium mb-3 text-card-foreground">
              Official Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="mayor"
                  className="block text-sm font-medium mb-2"
                >
                  Mayor
                </label>
                <input
                  id="mayor"
                  type="text"
                  name="mayor"
                  placeholder="Enter mayor name"
                  value={form.mayor}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
              <div>
                <label
                  htmlFor="ctc-no"
                  className="block text-sm font-medium mb-2"
                >
                  CTC No.
                </label>
                <input
                  id="ctc-no"
                  type="text"
                  name="ctc-no"
                  placeholder="Enter CTC number"
                  value={form["ctc-no"]}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
              <div>
                <label
                  htmlFor="or-no"
                  className="block text-sm font-medium mb-2"
                >
                  O.R. No.
                </label>
                <input
                  id="or-no"
                  type="text"
                  name="or-no"
                  placeholder="Enter O.R. number"
                  value={form["or-no"]}
                  onChange={handleTextChange}
                  className="w-full border border-input bg-background px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
            </div>
          </div>
          <Button
            onClick={generateDocument}
            disabled={isLoading}
            className="w-full"
            size="lg"
          >
            {isLoading ? "Generating..." : "Generate & Download .DOCX"}
          </Button>
        </div>
      </div>
    </div>
  );
}
